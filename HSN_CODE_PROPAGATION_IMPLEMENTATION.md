# HSN Code Auto-Propagation Implementation

## Overview
This document describes the implementation of auto-propagation logic for HSN code changes across Product, SKU, and Unit mappings in the SCM system.

## Implementation Details

### 1. Product Level HSN Code Propagation

**Location**: `SCMProductManagementServiceImpl.updateProduct()`

**Behavior**: When HSN code is updated at Product Master level, it automatically propagates to:
- All linked SKUs
- Unit-to-SKU mappings for all linked SKUs  
- SKU packaging tax mappings for all linked SKUs

**Implementation**:
```java
// Check if HSN/tax code has changed and propagate to SKUs and mappings
String oldTaxCode = oldProduct.getTaxCode();
String newTaxCode = productDefinition.getTaxCode();
if (!Objects.equals(oldTaxCode, newTaxCode)) {
    LOG.info("HSN code changed for product {} from {} to {}, propagating to SKUs and mappings", 
            productDefinition.getProductId(), oldTaxCode, newTaxCode);
    propagateHsnCodeFromProductToSkus(productDefinition.getProductId(), newTaxCode, userId);
}
```

### 2. SKU Level HSN Code Propagation

**Location**: `SCMProductManagementServiceImpl.updateSku()`

**Behavior**: When HSN code is updated at SKU level, it automatically propagates to:
- Unit-to-SKU mappings for that specific SKU
- SKU packaging tax mappings for that specific SKU
- Does NOT impact Product Master or other SKUs

**Implementation**:
```java
// Check if HSN/tax code has changed and propagate to mappings
String oldTaxCode = oldSku.getTaxCode();
String newTaxCode = skuDefinition.getTaxCode();
if (!Objects.equals(oldTaxCode, newTaxCode)) {
    LOG.info("HSN code changed for SKU {} from {} to {}, propagating to mappings", 
            skuDefinition.getSkuId(), oldTaxCode, newTaxCode);
    propagateHsnCodeFromSkuToMappings(skuDefinition.getSkuId(), newTaxCode, userId);
}
```

### 3. Unit-to-SKU Mapping Level HSN Code Handling

**Location**: `SkuMappingServiceImpl.updateUnitMappingsForSku()`

**Behavior**: HSN code changes at Unit-to-SKU mapping level remain local to that mapping only:
- No impact on Product Master
- No impact on other SKUs
- No impact on other mappings

**Implementation**: Already correctly implemented in existing code:
```java
mappedTaxCodes.forEach((unitId,taxCode) ->{
    if(taxCode !=null){
        for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
            if ((unitSkuMapping.getUnitId() == unitId && !taxCode.equals(unitSkuMapping.getTaxCode()))) {
                unitSkuMapping.setTaxCode(taxCode);
                dao.update(unitSkuMapping, true);
                break;
            }
        }
    }
});
```

## Helper Methods Added

### 1. propagateHsnCodeFromProductToSkus()
- Gets all SKUs linked to the product
- Updates tax code for each SKU
- Calls propagateHsnCodeFromSkuToMappings() for each SKU

### 2. propagateHsnCodeFromSkuToMappings()
- Updates Unit-SKU mappings for the SKU
- Updates SKU packaging tax mappings for the SKU

### 3. updateUnitSkuMappingTaxCodes()
- Batch updates tax codes in Unit-SKU mappings
- Includes proper audit trail (updatedBy, updatedAt)

### 4. updateSkuPackagingTaxMappingCodes()
- Batch updates tax codes in SKU packaging tax mappings
- Handles all packaging types for the SKU
- Includes proper audit trail

## Database Tables Affected

1. **PRODUCT_DEFINITION** - TAX_CATEGORY_CODE field
2. **SKU_DEFINITION** - TAX_CATEGORY_CODE field  
3. **UNIT_SKU_MAPPING** - TAX_CODE field
4. **SKU_PACKAGING_TAX_MAPPING** - TAX_CODE field

## API Endpoints

### Product Update
- **Endpoint**: `PUT /api/v1/product`
- **Controller**: `SCMProductManagementResources.updateProduct()`
- **Service**: `SCMProductManagementService.updateProduct()`

### SKU Update  
- **Endpoint**: `PUT /api/v1/sku`
- **Controller**: `SCMProductManagementResources.updateSku()`
- **Service**: `SCMProductManagementService.updateSku()`

### Unit-SKU Mapping Update
- **Endpoint**: `POST /api/v1/sku-mapping/update-unit-for-sku`
- **Controller**: `SkuMappingManagementResource.updateUnitMappingsForSku()`
- **Service**: `SkuMappingService.updateUnitMappingsForSku()`

## Logging

Comprehensive logging has been added at all levels:
- Product level changes
- SKU level changes  
- Mapping level updates
- Error handling

## Error Handling

- All propagation methods include try-catch blocks
- Errors are logged but don't prevent the main update operation
- Transactional integrity is maintained

## Testing Scenarios

### Test Case 1: Product Level Change
1. Update HSN code at Product Master level
2. Verify all linked SKUs get updated
3. Verify all Unit-SKU mappings get updated
4. Verify all SKU packaging tax mappings get updated

### Test Case 2: SKU Level Change  
1. Update HSN code at SKU level
2. Verify Product Master is NOT affected
3. Verify other SKUs are NOT affected
4. Verify Unit-SKU mappings for this SKU get updated
5. Verify SKU packaging tax mappings for this SKU get updated

### Test Case 3: Unit-SKU Mapping Level Change
1. Update HSN code at Unit-SKU mapping level
2. Verify Product Master is NOT affected
3. Verify SKU is NOT affected
4. Verify other Unit-SKU mappings are NOT affected
5. Verify only the specific mapping is updated

## Files Modified

1. `scm-core/src/main/java/com/stpl/tech/scm/core/service/impl/SCMProductManagementServiceImpl.java`
   - Added helper methods for HSN propagation
   - Modified updateProduct() method
   - Modified updateSku() method
   - Added necessary imports

## Dependencies

- Existing SkuMappingDao for database operations
- SCMCache for cache management
- AppConstants for status values
- SCMUtil for utility functions
