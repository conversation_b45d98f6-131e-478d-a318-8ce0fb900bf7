# HSN Code Propagation Testing Guide

## Prerequisites
1. Ensure the application is running
2. Have access to the database to verify changes
3. Have a test product with linked SKUs and mappings

## Test Scenarios

### Scenario 1: Product Level HSN Code Change

**Objective**: Verify that changing HSN code at Product Master level propagates to all linked entities.

**Steps**:
1. **Setup**:
   - Identify a test product with multiple SKUs
   - Note current HSN codes in:
     - PRODUCT_DEFINITION table (TAX_CATEGORY_CODE)
     - SKU_DEFINITION table (TAX_CATEGORY_CODE) for all linked SKUs
     - UNIT_SKU_MAPPING table (TAX_CODE) for all mappings
     - SKU_PACKAGING_TAX_MAPPING table (TAX_CODE) for all mappings

2. **Execute**:
   ```bash
   # API Call
   PUT /api/v1/product
   Content-Type: application/json
   
   {
     "productId": 123,
     "productName": "Test Product",
     "taxCode": "NEW_HSN_CODE_001",
     // ... other product fields
   }
   ```

3. **Verify**:
   - Check PRODUCT_DEFINITION: TAX_CATEGORY_CODE = "NEW_HSN_CODE_001"
   - Check all linked SKU_DEFINITION records: TAX_CATEGORY_CODE = "NEW_HSN_CODE_001"
   - Check all UNIT_SKU_MAPPING records: TAX_CODE = "NEW_HSN_CODE_001"
   - Check all SKU_PACKAGING_TAX_MAPPING records: TAX_CODE = "NEW_HSN_CODE_001"
   - Check application logs for propagation messages

**Expected Result**: All linked entities should have the new HSN code.

### Scenario 2: SKU Level HSN Code Change

**Objective**: Verify that changing HSN code at SKU level only affects that SKU's mappings.

**Steps**:
1. **Setup**:
   - Identify a test SKU with mappings
   - Note current HSN codes in:
     - PRODUCT_DEFINITION table (TAX_CATEGORY_CODE) - should NOT change
     - SKU_DEFINITION table (TAX_CATEGORY_CODE) for this SKU
     - Other SKU_DEFINITION records - should NOT change
     - UNIT_SKU_MAPPING table (TAX_CODE) for this SKU only
     - SKU_PACKAGING_TAX_MAPPING table (TAX_CODE) for this SKU only

2. **Execute**:
   ```bash
   # API Call
   PUT /api/v1/sku
   Content-Type: application/json
   
   {
     "skuId": 456,
     "skuName": "Test SKU",
     "taxCode": "NEW_HSN_CODE_002",
     // ... other SKU fields
   }
   ```

3. **Verify**:
   - Check PRODUCT_DEFINITION: TAX_CATEGORY_CODE unchanged
   - Check target SKU_DEFINITION: TAX_CATEGORY_CODE = "NEW_HSN_CODE_002"
   - Check other SKU_DEFINITION records: TAX_CATEGORY_CODE unchanged
   - Check UNIT_SKU_MAPPING for this SKU: TAX_CODE = "NEW_HSN_CODE_002"
   - Check UNIT_SKU_MAPPING for other SKUs: TAX_CODE unchanged
   - Check SKU_PACKAGING_TAX_MAPPING for this SKU: TAX_CODE = "NEW_HSN_CODE_002"
   - Check SKU_PACKAGING_TAX_MAPPING for other SKUs: TAX_CODE unchanged

**Expected Result**: Only the target SKU and its mappings should have the new HSN code.

### Scenario 3: Unit-SKU Mapping Level HSN Code Change

**Objective**: Verify that changing HSN code at mapping level remains local.

**Steps**:
1. **Setup**:
   - Identify a specific Unit-SKU mapping
   - Note current HSN codes in:
     - PRODUCT_DEFINITION table (TAX_CATEGORY_CODE) - should NOT change
     - SKU_DEFINITION table (TAX_CATEGORY_CODE) - should NOT change
     - Other UNIT_SKU_MAPPING records - should NOT change
     - Target UNIT_SKU_MAPPING record

2. **Execute**:
   ```bash
   # API Call
   POST /api/v1/sku-mapping/update-unit-for-sku
   Content-Type: application/json
   
   {
     "skuId": 456,
     "unitIds": [789],
     "mappedTaxCodes": {
       "789": "NEW_HSN_CODE_003"
     }
     // ... other mapping fields
   }
   ```

3. **Verify**:
   - Check PRODUCT_DEFINITION: TAX_CATEGORY_CODE unchanged
   - Check SKU_DEFINITION: TAX_CATEGORY_CODE unchanged
   - Check target UNIT_SKU_MAPPING (unitId=789, skuId=456): TAX_CODE = "NEW_HSN_CODE_003"
   - Check other UNIT_SKU_MAPPING records: TAX_CODE unchanged

**Expected Result**: Only the specific Unit-SKU mapping should have the new HSN code.

## Database Queries for Verification

### Check Product HSN Code
```sql
SELECT PRODUCT_ID, PRODUCT_NAME, TAX_CATEGORY_CODE 
FROM PRODUCT_DEFINITION 
WHERE PRODUCT_ID = ?;
```

### Check SKU HSN Codes
```sql
SELECT SKU_ID, SKU_NAME, TAX_CATEGORY_CODE, PRODUCT_ID 
FROM SKU_DEFINITION 
WHERE PRODUCT_ID = ?;
```

### Check Unit-SKU Mapping HSN Codes
```sql
SELECT UNIT_SKU_MAPPING_ID, UNIT_ID, SKU_ID, TAX_CODE 
FROM UNIT_SKU_MAPPING 
WHERE SKU_ID = ?;
```

### Check SKU Packaging Tax Mapping HSN Codes
```sql
SELECT SKU_PACKAGING_TAX_MAPPING_ID, SKU_ID, PACKAGING_ID, UNIT_ID, TAX_CODE 
FROM SKU_PACKAGING_TAX_MAPPING 
WHERE SKU_ID = ?;
```

## Log Messages to Look For

### Product Level Change
```
HSN code changed for product {productId} from {oldCode} to {newCode}, propagating to SKUs and mappings
Propagating HSN code {newCode} from product {productId} to all linked SKUs
Updated SKU {skuId} tax code from {oldCode} to {newCode}
Updated {count} Unit-SKU mappings for SKU {skuId}
Updated {count} SKU packaging tax mappings for SKU {skuId}
```

### SKU Level Change
```
HSN code changed for SKU {skuId} from {oldCode} to {newCode}, propagating to mappings
Propagating HSN code {newCode} from SKU {skuId} to its mappings
Updated {count} Unit-SKU mappings for SKU {skuId}
Updated {count} SKU packaging tax mappings for SKU {skuId}
```

## Error Scenarios to Test

1. **Invalid HSN Code**: Test with invalid/null HSN codes
2. **Non-existent Product/SKU**: Test with invalid IDs
3. **Database Constraints**: Test with HSN codes that violate constraints
4. **Concurrent Updates**: Test simultaneous updates to same entities

## Performance Considerations

- Monitor execution time for products with many SKUs
- Check database connection pool usage during bulk updates
- Verify transaction rollback behavior on errors

## Rollback Testing

1. Test transaction rollback when propagation fails
2. Verify partial updates don't occur
3. Check data consistency after failed operations
