/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('wastageCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil',
        'productService','$toastService','$alertService','metaDataService','previewModalService',
        function ($rootScope, $scope, apiJson, $http, appUtil,
                  productService,$toastService,$alertService,metaDataService,previewModalService) {

            $scope.checkEmpty = appUtil.checkEmpty;
            $scope.showPreview = previewModalService.showPreview;

            function getWastageEvents(unitId,businessDate, callback) {
                $http({
                    method:'POST',
                    url:apiJson.urls.stockManagement.wastageEvents,
                    data:{
                        unitId:unitId,
                        businessDate:businessDate
                    }
                }).then(function(response){
                    callback(response.data);
                },function(response){
                   console.log("got error",response);
                });
            }

            function getWastageEventsForDays(unitId,startDate,endDate, callback) {
                $http({
                    method:'POST',
                    url:apiJson.urls.stockManagement.wastageEventsForDays,
                    params:{
                        startDate:startDate,
                        endDate:endDate,
                        unitId:unitId
                    }
                }).then(function(response){
                    callback(response.data);
                },function(response){
                    console.log("got error",response);
                });
            }
            
            $scope.init = function () {
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.unitId = $scope.currentUser.unitId;
                $scope.userId = $scope.currentUser.userId;
                $scope.comment = null;
                $scope.minDate = appUtil.getTimeInPast(7);
                $scope.businessDate = appUtil.getCurrentBusinessDate();
                $scope.wastedItems = [];
                $scope.todaysWastage = [];
                $scope.wastageEvents = [];
                $scope.products = appUtil.getActiveScmProducts();
                setPackagingByProducts();
                $scope.selectedProductId = $scope.products[0].productId;
                getWastageEvents($scope.unitId,appUtil.getCurrentBusinessDate(), function(items){
                    $scope.todaysWastage = items;
                    $scope.groupTodayWastageByProductId(items);
                });
                $scope.productInventoryMap = {};
                $scope.getProductInventoryData();
            };

            function calculateWastage(item) {
                console.log("item is : ",item);
                var currentQuantity = item.quantity;
                item.inventoryCheck = false;
                item.quantity = 0;
                for (var index in item.productPackagings) {
                    var mapping = item.productPackagings[index];
                    if (!appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.packaging.quantity)) {
                        item.quantity += (mapping.packaging.quantity * mapping.packaging.conversionRatio);
                    }
                }

                if (item.quantity < 0) {
                    $toastService.create("Wastage quantity cannot be less than zero");
                    item.quantity = currentQuantity;
                    item.inventoryCheck = false;
                } else {
                    var productInventory = $scope.productInventoryMap[item.productId];
                    if (productInventory != undefined && productInventory != null) {
                        if (item.quantity > productInventory) {
                            $toastService.create("Wastage Exceeded the available stock for " + item.product.productName + " ...!");
                            item.inventoryCheck = true;
                            item.availableStock = productInventory;
                        }
                    }
                    else {
                        $toastService.create("No inventory Found for Product " + item.product.productName + " ...!");
                    }
                }
            }

            $scope.productFilter = function (value) {
                return value.autoProduction == false;
            };

            $scope.AddBulkWastage = function () {
                $scope.wastedItems = [];
                $scope.missedBulkWastage = [];
                $scope.missedProducts = [];
                angular.forEach($scope.productInventoryMap, function (value, key) {
                    if (value > 0) {
                        $scope.addNewItem(key, value);
                    }
                });
                if ($scope.missedBulkWastage.length > 0) {
                    $toastService.create("Unable to set Quantities for Product : " + $scope.missedBulkWastage.join(","));
                }
                console.log("Missed Prods in cache ", $scope.missedProducts);
            };

            $scope.changeQuantity = function(item, mapping, value) {
                mapping.packaging.quantity = value;
                calculateWastage(item);
            };

            $scope.removeMapping = function(item,mapping){
                mapping.packaging.quantity = undefined;
                calculateWastage(item);
            };

            function getPackagings(mappingList,packagingMap) {
                for(var key in mappingList){
                    mappingList[key].packaging = angular.copy(packagingMap[mappingList[key].packagingId]);
                }
                return mappingList;
            }

            function setPackagingByProducts() {
                metaDataService.getAllPackagingMappings(function (mappings) {
                    $scope.packagingMap = appUtil.getPackagingMap();
                    $scope.productPackagings = [];
                    for(var product in mappings){
                        try{
                            $scope.productPackagings[product] = getPackagings(mappings[product],$scope.packagingMap);
                        }catch(e) {
                            console.log(product, mappings[product]);
                        }
                    }
                });
            }

            $scope.getWastageEvents = function(){
                if(!appUtil.checkEmpty($scope.selectedDate)){
                    getWastageEvents($scope.unitId,$scope.selectedDate, function(items){
                        $scope.wastageEvents = items;
                        $scope.groupTodayWastageByProductId(items);
                    });
                }else {
                    $toastService.create("Please select a date first");
                }
            };

            function checkForMonkWastage(wastageEvent) {
                var flag = false;
                for(var i in wastageEvent.items){
                    var item = wastageEvent.items[i];
                    if(item.comment.indexOf("Monk Wastage")!=-1){
                        flag = true;
                        break;
                    }
                }
                return flag;
            }

            $scope.cancelWastage = function(item){
                var currentStatus = item.status;

                if(checkForMonkWastage(item)){
                    $toastService.create("Monk wastage cannot be cancelled!");
                    return;
                }

                $alertService.confirm("Are you sure?","",function (retVal) {
                    if (retVal){
                        $http({
                            method:'POST',
                            url:apiJson.urls.stockManagement.cancelWastageEvent,
                            data:item.wastageId
                        }).then(function(response){
                            if(!appUtil.checkEmpty(response) && response.data){
                                getWastageEvents($scope.unitId,appUtil.getCurrentBusinessDate(), function(items){
                                    $scope.todaysWastage = items;
                                    $scope.groupTodayWastageByProductId(items);
                                });
                                reCheckInventory($scope.wastedItems);
                                $toastService.create("Cancelled wastage successfully");
                            }else{
                                $toastService.create("Wastage cannot be cancelled after submitting inventory for current business date");
                            }
                        },function(response){
                            console.log("Got error ::::",response);
                        });
                    }
                });
            };


            $scope.initializeModal = function(){
                $scope.wastageEvents = [];
                $scope.maxWastageDate = appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd");
                $scope.wastageStartDate = null;
                $scope.wasatgeEndDate = null;
            };

            $scope.setStartDate = function (startDate) {
                $scope.wastageStartDate = startDate;
                $scope.setEndDate(null);
                $scope.wastageEvents = [];
                $scope.groupedSkuWastageList = [];
            };

            $scope.setEndDate = function (endDate) {
                if ($scope.wastageStartDate == undefined || $scope.wastageStartDate == null || $scope.wastageStartDate == '') {
                    $toastService.create("Please Select the Start Date first..!");
                    $scope.wasatgeEndDate = null;
                    return false;
                }
                $scope.wasatgeEndDate = endDate;
                $scope.wastageEvents = [];
                $scope.groupedProductWastageList = [];
            };

            $scope.getWastageEventsForDays = function(){
                if (appUtil.checkEmpty($scope.wastageStartDate)) {
                    $toastService.create("Please select a start Date..!");
                    return false;
                }
                if (appUtil.checkEmpty($scope.wasatgeEndDate)) {
                    $toastService.create("Please select an End Date..!");
                    return false;
                }
                getWastageEventsForDays($scope.unitId,$scope.wastageStartDate,$scope.wasatgeEndDate, function(items){
                    $scope.wastageEvents = items;
                    if(items == null || items.length == 0){
                        $toastService.create("No Wastage Events found");
                    }
                    else {
                        $scope.groupWastageByProductId($scope.wastageEvents);
                    }
                });
            };

            $scope.groupWastageByProductId = function (list) {
                var wastageItemList = [];
                angular.forEach(list,function (wastage) {
                    angular.forEach(wastage.items,function (item) {
                        wastageItemList.push(item);
                    });
                });
                console.log("wastage Items list is : ",wastageItemList);
                $scope.groupedProductWastageList = [];
                var productList = [];
                for(var i in wastageItemList){
                    if(productList.indexOf(wastageItemList[i].productId) < 0) {
                        var groupedByProductIdListObj = getGroupedByProductListObj();
                        console.log("i item is ",wastageItemList[i]);
                        groupedByProductIdListObj.productId = wastageItemList[i].productId;
                        groupedByProductIdListObj.details.productName = wastageItemList[i].product.productName;
                        groupedByProductIdListObj.details.unitOfMeasure = wastageItemList[i].product.unitOfMeasure;
                        groupedByProductIdListObj.details.total++;
                        groupedByProductIdListObj.details.totalAmount += wastageItemList[i].totalAmount;
                        groupedByProductIdListObj.details.totalQuantity += wastageItemList[i].quantity;
                        groupedByProductIdListObj.details.productWastageList.push(wastageItemList[i]);
                        $scope.groupedProductWastageList.push(groupedByProductIdListObj);
                        productList.push(wastageItemList[i].productId);
                    } else {
                        for(var j in $scope.groupedProductWastageList){
                            if($scope.groupedProductWastageList[j].productId == wastageItemList[i].productId){
                                $scope.groupedProductWastageList[j].details.total++;
                                $scope.groupedProductWastageList[j].details.totalAmount += wastageItemList[i].totalAmount;
                                $scope.groupedProductWastageList[j].details.totalQuantity += wastageItemList[i].quantity;
                                $scope.groupedProductWastageList[j].details.productWastageList.push(wastageItemList[i]);
                            }
                        }
                    }
                }
                console.log("grouped is ", $scope.groupedProductWastageList);
            };

            $scope.groupTodayWastageByProductId = function (list) {
                var items = [];
                angular.forEach(list,function (wastage) {
                    angular.forEach(wastage.items,function (item) {
                        items.push(item);
                    });
                });
                console.log("items are : ",items);
                $scope.groupedTodayProductWastageList = [];
                var todayProductList = [];
                for(var i in items){
                    if(todayProductList.indexOf(items[i].productId) < 0) {
                        var groupedTodayByProductIdListObj = getGroupedByProductListObj();
                        console.log("i item is ",items[i]);
                        groupedTodayByProductIdListObj.productId = items[i].productId;
                        groupedTodayByProductIdListObj.details.productName = items[i].product.productName;
                        groupedTodayByProductIdListObj.details.unitOfMeasure = items[i].product.unitOfMeasure;
                        groupedTodayByProductIdListObj.details.total++;
                        groupedTodayByProductIdListObj.details.totalAmount += items[i].totalAmount;
                        groupedTodayByProductIdListObj.details.totalQuantity += items[i].quantity;
                        groupedTodayByProductIdListObj.details.productWastageList.push(items[i]);
                        $scope.groupedTodayProductWastageList.push(groupedTodayByProductIdListObj);
                        todayProductList.push(items[i].productId);
                    } else {
                        for(var j in $scope.groupedTodayProductWastageList){
                            if($scope.groupedTodayProductWastageList[j].productId == items[i].productId){
                                $scope.groupedTodayProductWastageList[j].details.total++;
                                $scope.groupedTodayProductWastageList[j].details.totalAmount += items[i].totalAmount;
                                $scope.groupedTodayProductWastageList[j].details.totalQuantity += items[i].quantity;
                                $scope.groupedTodayProductWastageList[j].details.productWastageList.push(items[i]);
                            }
                        }
                    }
                }
                console.log("grouped is ", $scope.groupedTodayProductWastageList);
            };

            function getGroupedByProductListObj(){
                var obj = {};
                obj.productId = null;
                var innerObj = {};
                innerObj.productName = "";
                innerObj.total = 0;
                innerObj.totalAmount = 0;
                innerObj.totalQuantity = 0;
                innerObj.unitOfMeasure = "";
                innerObj.productWastageList = [];
                obj.details = innerObj;
                return obj;
            }

            $scope.closeWastageView = function () {
                $scope.wastageEvents = [];
                $scope.groupedProductWastageList = [];
                $scope.isByProduct = false;
            };

            function getEventItems(list) {
                var result = [];
                angular.forEach(list,function (wastage) {
                    angular.forEach(wastage.items,function (item) {
                        result.push(item);
                    });
                });
                return result;
            }

            $scope.generateWastageSheet = function () {
                $http({
                    url: apiJson.urls.stockManagement.generateWastageSheet,
                    method: 'POST',
                    responseType: 'arraybuffer',
                    data: getEventItems($scope.wastageEvents),
                    params: {
                        "unitId":$scope.unitId
                    },
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                }).success(
                    function (data) {
                        var startDateString = appUtil.getFormattedDate($scope.wastageStartDate);
                        var endDateString = appUtil.getFormattedDate($scope.wasatgeEndDate);
                        var unit = appUtil.getUnitData();
                        var fileName =  "Wastages_" + unit.name+"_" +startDateString+"_to_"+endDateString + ".xlsx";
                        var blob = new Blob(
                            [data],
                            {
                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            }, fileName);
                        saveAs(blob, fileName);
                    }).error(function (err) {
                    console.log("Error during getting data", err);
                });
            }


            function setPackagingByProduct(productId) {
                return angular.copy($scope.productPackagings[productId]);
            }

            $scope.addNewItem = function(selectedProductId, qty) {
                var found = false;
                if(!appUtil.isEmptyObject($scope.businessDate) && !appUtil.checkEmpty(selectedProductId)){
                    $scope.wastedItems.forEach(function (roi) {
                        if(roi.product.productId == selectedProductId){
                            found = true;
                            Materialize.toast("Product already added!");
                            return false;
                        }
                    });
                    if(!found){
                        var selectedProduct = productService.getProduct(selectedProductId);
                        if (selectedProduct != null) {
                            var item = {
                                product: selectedProduct,
                                productId: selectedProduct.productId,
                                quantity: 0,
                                uom: selectedProduct.unitOfMeasure,
                                productPackagings: setPackagingByProduct(selectedProduct.productId),
                                comment: "Wasted",
                                enteredComment: null
                            };
                            $scope.wastedItems.unshift(item);
                            if (qty != undefined && qty != null && qty > 0) {
                                var check = false;
                                angular.forEach(item.productPackagings, function (pack) {
                                    if (pack.packaging.packagingType == "LOOSE") {
                                        check = true;
                                        $scope.changeQuantity(item, pack, qty);
                                    }
                                });
                                if (!check) {
                                    $scope.missedBulkWastage.push(selectedProduct.productName);
                                }
                            }
                        } else {
                            $toastService.create("Can not find Product Definition for : " +selectedProductId);
                            $scope.missedProducts.push(selectedProductId);
                        }
                        console.log("items is ",$scope.wastedItems);
                    }
                }else{
                    if(appUtil.isEmptyObject($scope.businessDate)){
                        $toastService.create("Please select a valid business date before adding wastage");
                    }
                    if(appUtil.checkEmpty(selectedProductId)){
                        $toastService.create("Please select a valid product before adding wastage");
                    }
                }
            };

            $scope.getProductInventoryData = function (wastedItems) {
                $scope.availableInventoryProductIds = [];
                $http({
                    url: apiJson.urls.stockManagement.cafeProductStockAtHand,
                    method: 'GET',
                    params: {
                        unitId: $scope.unitId,
                        keyType: "PRODUCT"
                    }
                }).then(function success(stock) {
                    if (appUtil.isEmptyObject(stock.data)) {
                        $toastService.create("could not fetch current Inventory for this unit");
                    } else {
                        $scope.productInventoryMap = stock.data;
                        angular.forEach(stock.data , function (value , key) {
                              if (value > 0) {
                                  $scope.availableInventoryProductIds.push(key);
                              }
                        });
                        console.log("inventory map is : ",$scope.productInventoryMap);
                        if (wastedItems != undefined  && wastedItems != null) {
                            angular.forEach(wastedItems, function (item) {
                                calculateWastage(item);
                            });
                        }
                    }
                }, function error() {
                    $toastService.create("could not fetch current stock at hand for this unit");
                });
            };

            $scope.removeItem = function(index){
                $scope.wastedItems.splice(index,1);
            };

            function reCheckInventory(wastedItems) {
                $scope.getProductInventoryData(wastedItems);
            }

            $scope.sendWastageItems = function (wastedItems) {
                    var wastage = {
                        unitId: $scope.unitId,
                        status: "SETTLED",
                        businessDate: $scope.businessDate,
                        generatedBy: $scope.userId,
                        items: []
                    };
                    console.log("Wasted Items ::::", wastedItems);
                    var checkInventory = false;
                    var listOfProducts = [];
                    angular.forEach(wastedItems, function (item) {
                        if (item.inventoryCheck) {
                            checkInventory = true;
                            listOfProducts.push(item.product.productName);
                        }
                    });
                    if (checkInventory) {
                        var message = listOfProducts.join(",");
                        $toastService.create("Please Check the inventory of the items : " + message);
                        return false;
                    }
                    if (wastedItems.length > 0) {
                        if (checkIfValid(wastedItems)) {
                            $alertService.confirm("Are you sure?", "Please check if the values are correct.",
                                function (result) {
                                    if (result) {
                                        //removing packagingMappings from product
                                        var wastedItemsFinal = angular.copy(wastedItems);
                                        wastedItemsFinal = wastedItemsFinal.map(function (item) {
                                            item.price = item.product.unitPrice;
                                            item.cost = item.product.unitPrice * item.quantity;
                                            delete item.productPackagings;
                                            delete item.product;
                                            return item;
                                        });
                                        wastedItemsFinal.forEach(function (item) {
                                            wastage.items.push(item);
                                        });
                                        var wastages = [];
                                        wastages.push(wastage);
                                        $http({
                                            method: 'POST',
                                            url: apiJson.urls.stockManagement.wastageEvent,
                                            data: wastages,
                                            params: {
                                                "isManual" : "Yes"
                                            }
                                        }).then(function (response) {
                                            if (response.data != undefined && response.data != null) {
                                                $toastService.create("Added wastage successfully");
                                                var result = response.data;
                                                console.log("result is ", result);
                                                if (result[0].autoBookedProducts != null && result[0].autoBookedProducts.length > 0) {
                                                    var bookedProducts = result[0].autoBookedProducts.join();
                                                    $toastService.create("Production Booking Done for Products : " + bookedProducts);
                                                }
                                                $scope.init();
                                            } else {
                                                $toastService.create("Could not add wastage items! Please try again later...");
                                            }
                                        }, function (response) {
                                            if (response.data.errorCode != null) {
                                                if (response.data.errorTitle == 'Inventory Not Available') {
                                                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, reCheckInventory(wastedItems), true);
                                                } else {
                                                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                                                    $scope.init();
                                                }
                                            } else {
                                                $toastService.create("Could not Add Wastage...!");
                                                console.log("error:" + response);
                                                $scope.init();
                                            }
                                        });
                                    }
                                }
                            );
                        } else {
                            $toastService.create("Wrong values! Please check again");
                        }
                    }
            };


            function checkIfValid(wastedItems){
                var zeroItemList = wastedItems.filter(function(item){
                    var quantity = item.quantity;
                    if(quantity==undefined || quantity == 0 || quantity < 0){
                        return false;
                    }
                    if($scope.userId == 125200 || $scope.userId == 140199) {
                        return true;
                    }
                    if((item.uom == 'KG' || item.uom == 'L') && quantity <= 500) {
                            return true;
                    }
                    if((item.uom == 'PC' || item.uom == 'SACHET' || item.uom == 'PKT') && quantity <= 1000) {
                        return true;
                    }
                    if(item.uom == 'M' || item.uom == 'KM') {
                        return true;
                    }
                    return false;
                });
                if (zeroItemList.length == wastedItems.length) {
                    return true;
                }
                else {
                    var missedProductNames = [];
                    for (var i=0;i<wastedItems.length;i++) {
                        var found = false;
                        for (var j = 0; j < zeroItemList.length; j++) {
                            if (wastedItems[i].product.productId == zeroItemList[j].product.productId) {
                                found = true;
                                break;
                            }
                        }
                        if (!found) {
                            missedProductNames.push(wastedItems[i].product.productName);
                        }
                    }
                    $toastService.create("Please Enter the wastage for all the Products's ...!  "+missedProductNames.join(","));
                    return false;
                }
            }
        }
    ]
);