package com.stpl.tech.scm.service.controller;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.FullfillmentReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.FULLFILLMENT_REPORT_CONTEXT)
public class FullfillmentReportResource {

    @Autowired
    FullfillmentReportService fullfillmentReportService;

    @GetMapping(value = "start/report")
    Boolean startFullfillmentReport() throws SumoException {
     return fullfillmentReportService.startFullfillmentReportProcess();
    }

    @Scheduled(cron="0 30 14 * * *", zone = "GMT+05:30")
    @GetMapping(value = "/start-report")
    Boolean startFullfillmentReportProcess() throws SumoException {
        return fullfillmentReportService.startFullfillmentReportProcess();
    }

}
