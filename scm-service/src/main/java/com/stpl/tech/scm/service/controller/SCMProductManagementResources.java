package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.recipe.model.RecipeCost;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.SCMProductPriceManagementService;
import com.stpl.tech.scm.core.util.MultiPartFileHelper;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PlanOrderItem;
import com.stpl.tech.scm.domain.model.PriceUpdateEvent;
import com.stpl.tech.scm.domain.model.PriceUpdateEventActionType;
import com.stpl.tech.scm.domain.model.PriceUpdateEventStatus;
import com.stpl.tech.scm.domain.model.PriceUpdateEventType;
import com.stpl.tech.scm.domain.model.PriceUpdateEventVO;
import com.stpl.tech.scm.domain.model.ProductBasicDetail;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.ProductPriceData;
import com.stpl.tech.scm.domain.model.RPPMappingRequest;
import com.stpl.tech.scm.domain.model.RegionProductPackagingMapping;
import com.stpl.tech.scm.domain.model.RequestedSkuDetails;
import com.stpl.tech.scm.domain.model.SkuAttributeValue;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.UnitDerivedProductMappingDto;
import com.stpl.tech.scm.domain.model.UnitProductPackagingMapping;
import com.stpl.tech.scm.domain.model.UnitProductsVO;
import com.stpl.tech.scm.domain.model.UserRequestDto;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.PRODUCT_MANAGEMENT_ROOT_CONTEXT)
public class SCMProductManagementResources extends AbstractResources {

	private final Logger LOG = LoggerFactory.getLogger(SCMProductManagementResources.class);

	@Autowired
	private SCMProductManagementService scmProductManagementService;

	@Autowired
	private SCMProductPriceManagementService scmProductPriceManagementService;

	@Autowired
	EnvProperties env;

	// Product Resources

	@RequestMapping(method = RequestMethod.GET, value = "product", produces = MediaType.APPLICATION_JSON)
	public ProductDefinition viewProduct(@RequestParam(value = "productId") final int productId) {
		return scmProductManagementService.viewProduct(productId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "products", produces = MediaType.APPLICATION_JSON)
	public List<ProductDefinition> viewAllProducts(@RequestParam(required = false) Integer unitId,
			@RequestParam(required = false) Boolean getArchived) {
		if (getArchived == null) {
			getArchived = false;
		}
		return scmProductManagementService.viewAllProducts(unitId, getArchived);
	}

	@RequestMapping(method = RequestMethod.GET, value = "products-basic-detail", produces = MediaType.APPLICATION_JSON)
	public List<ProductBasicDetail> viewAllProductsBasicDetail(@RequestParam(required = false) Integer unitId,
															   @RequestParam(required = false) Boolean getArchived) {
		if (getArchived == null) {
			getArchived = false;
		}
		return scmProductManagementService.viewAllBasicProducts(unitId, getArchived);
	}

	@RequestMapping(method = RequestMethod.GET, value = "products-basic-detail-for-recipe", produces = MediaType.APPLICATION_JSON)
	public List<ProductBasicDetail> viewAllProductsBasicDetailForRecipe(@RequestParam Boolean isScm) {
		return scmProductManagementService.viewAllBasicProductsForRecipe(isScm);
	}

	@RequestMapping(method = RequestMethod.GET, value = "product-maps", produces = MediaType.APPLICATION_JSON)
	public Map<Integer, ProductDefinition> getAllProductMaps() {
		return scmProductManagementService.getAllProductMaps();
	}

	@RequestMapping(method = RequestMethod.GET, value = "variant-product-maps", produces = MediaType.APPLICATION_JSON)
	public Map<Integer, ProductDefinition> getAllVariantProductMaps() {
		return scmProductManagementService.getAllVariantProductMaps();
	}

	@RequestMapping(method = RequestMethod.POST, value = "product", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public ProductDefinition addNewProduct( HttpServletRequest request,@RequestBody final ProductDefinition productDefinition)
			throws Exception {
		return scmProductManagementService.addNewProduct(productDefinition, getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.POST, value = "user-creation-product", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public ProductDefinition addNewProductV2(HttpServletRequest request,@RequestBody final ProductDefinition productDefinition) throws DataUpdationException, SumoException {
		return scmProductManagementService.addNewProductV2(productDefinition, getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.POST, value = "cancel-product", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public ProductDefinition cancelProduct(HttpServletRequest request,@RequestBody final ProductDefinition productDefinition) throws SumoException {
		return scmProductManagementService.cancelProduct(productDefinition, getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.PUT, value = "user-creation-product", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public ProductDefinition updateProductV2(HttpServletRequest request,@RequestBody final ProductDefinition productDefinition) throws DataUpdationException, SumoException {
		return scmProductManagementService.updateProductV2(productDefinition, getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.PUT, value = "product", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public ProductDefinition updateProduct(@RequestBody final ProductDefinition productDefinition , HttpServletRequest request)
			throws DataUpdationException, SumoException {
		return scmProductManagementService.updateProduct(productDefinition,getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.POST, value = "get-user-created-products", produces = MediaType.APPLICATION_JSON)
	public List<ProductDefinition> getUserCreatedProducts(@RequestBody UserRequestDto productRequest) {
		return scmProductManagementService.getAllUserCreatedProducts(productRequest);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "product-deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean deactivateProduct(@RequestBody final int productId) {
		return scmProductManagementService.deactivateProduct(productId);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "product-activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean activateProduct(@RequestBody final int productId) throws DataUpdationException {
		return scmProductManagementService.activateProduct(productId);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "product-archive", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean archiveProduct(@RequestBody final int productId) {
		return scmProductManagementService.archiveProduct(productId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload-product-image", consumes = MediaType.MULTIPART_FORM_DATA)
	public String uploadProductImage(HttpServletRequest request,
										 @RequestParam(value = "mimeType") MimeType mimeType,
										 @RequestParam(value = "productId") Integer productId,
										 @RequestParam(value = "file") final MultipartFile file) throws SumoException {
		return scmProductManagementService.uploadProductImage(mimeType, productId, file);
	}

	// Packaging resources

	@RequestMapping(method = RequestMethod.GET, value = "packagings", produces = MediaType.APPLICATION_JSON)
	public List<PackagingDefinition> viewAllPackaging() {
		return scmProductManagementService.viewAllPackaging();
	}

	@RequestMapping(method = RequestMethod.GET, value = "packaging-map", produces = MediaType.APPLICATION_JSON)
	public Map<Integer, PackagingDefinition> getAllPackagingMap() {
		return scmProductManagementService.getAllPackagingMap();
	}

	@RequestMapping(method = RequestMethod.GET, value = "packaging", produces = MediaType.APPLICATION_JSON)
	public PackagingDefinition viewPackaging(@RequestParam(value = "productId") final int packagingId) {
		return scmProductManagementService.viewPackaging(packagingId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "packaging", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean addNewPackaging(@RequestBody final List<PackagingDefinition> packagingDefinitions) throws SumoException {
		return scmProductManagementService.addNewPackaging(packagingDefinitions);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "packaging", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean updatePackaging(@RequestBody final PackagingDefinition packagingDefinition) {
		return scmProductManagementService.updatePackaging(packagingDefinition);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "packaging-activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean activatePackaging(@RequestBody final int packagingDefinitionId) {
		return scmProductManagementService.activatePackaging(packagingDefinitionId);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "packaging-deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean deactivatePackaging(@RequestBody final int packagingDefinitionId) {
		return scmProductManagementService.deactivatePackaging(packagingDefinitionId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "sub-packaging-mapping", produces = MediaType.APPLICATION_JSON)
	public Map<String, Set<Map<String, Integer>>> getAllSubPackagingMapping() {
		return scmProductManagementService.getAllSubPackagingMapping();
	}

	// Product Packaging Mapping Resources

	@RequestMapping(method = RequestMethod.GET, value = "product-packaging-mappings", produces = MediaType.APPLICATION_JSON)
	public Map<Integer, List<ProductPackagingMapping>> viewAllProductPackagingMapping() {
		return scmProductManagementService.viewAllProductPackagingMapping();
	}

	@RequestMapping(method = RequestMethod.GET, value = "default-product-packaging-mappings", produces = MediaType.APPLICATION_JSON)
	public Map<Integer, List<UnitProductPackagingMapping>> viewUnitProductPackagingMapping(@RequestParam final int unitId) {
		return scmProductManagementService.viewUnitProductPackagingMapping(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "product-packaging-mapping", produces = MediaType.APPLICATION_JSON)
	public ProductPackagingMapping viewProductPackagingMapping(@RequestParam final int productPackagingMappingId) {
		return scmProductManagementService.viewProductPackagingMapping(productPackagingMappingId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "product-packaging-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public List<ProductPackagingMapping> addNewProductPackagingMapping(
			@RequestBody final List<ProductPackagingMapping> productPackagingMappings) throws SumoException {
		return scmProductManagementService.addNewProductPackagingMapping(productPackagingMappings);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "product-packaging-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean updateProductPackagingMapping(
			@RequestBody final List<ProductPackagingMapping> productPackagingMappings) {
		return scmProductManagementService.updateProductPackagingMapping(productPackagingMappings);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "product-packaging-mapping-activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean activateProductPackagingMapping(@RequestBody final int productPackagingMappingId) {
		return scmProductManagementService.activateProductPackagingMapping(productPackagingMappingId);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "product-packaging-mapping-deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean deactivateProductPackagingMapping(@RequestBody final int productPackagingMappingId) {
		return scmProductManagementService.deactivateProductPackagingMapping(productPackagingMappingId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "product-packaging-mapping-by-product", produces = MediaType.APPLICATION_JSON)
	public List<ProductPackagingMapping> getProductPackagingMappingByProduct(@RequestParam final int productId) {
		return scmProductManagementService.getProductPackagingMappingByProduct(productId);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "set-default-product-packaging-mapping", produces = MediaType.APPLICATION_JSON)
	public boolean setDefaultProductPackaging(@RequestBody final int productPackagingMappingId)
			throws DataUpdationException {
		return scmProductManagementService.setDefaultProductPackaging(productPackagingMappingId);
	}

	// SKU resources

	@RequestMapping(method = RequestMethod.GET, value = "skus", produces = MediaType.APPLICATION_JSON)
	public List<SkuDefinition> viewAllSku() {
		return scmProductManagementService.viewAllSku();
	}

	@RequestMapping(method = RequestMethod.GET, value = "skus-by-products", produces = MediaType.APPLICATION_JSON)
	public Map<Integer, List<SkuDefinition>> viewAllSkuByProduct() {
		return scmProductManagementService.viewAllSkuByProduct();
	}

	@RequestMapping(method = RequestMethod.POST, value = "skus-by-unit-product", produces = MediaType.APPLICATION_JSON)
	public Map<Integer, List<SkuDefinition>> viewAllActiveSkuByUnitId(@RequestParam(value = "unitId") Integer unitId, @RequestBody final List<Integer> productIds) throws SumoException {
		return scmProductManagementService.viewAllActiveSkuByUnitId(unitId,productIds);
	}

	@RequestMapping(method = RequestMethod.GET, value = "sku", produces = MediaType.APPLICATION_JSON)
	public SkuDefinition viewSku(@RequestParam final int skuId) throws SumoException {
		return scmProductManagementService.viewSku(skuId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public SkuDefinition addNewSku(HttpServletRequest request,@RequestBody final SkuDefinition skuDefinition) throws Exception {
		return scmProductManagementService.addNewSku(skuDefinition, getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku/add/V2", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean addNewSkuV2(HttpServletRequest request,@RequestBody final SkuDefinition skuDefinition) throws Exception {
		return scmProductManagementService.addNewSkuV2(skuDefinition, getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku/get-all-requests", produces = MediaType.APPLICATION_JSON)
	public List<RequestedSkuDetails> getAllSkuRequests(@RequestBody UserRequestDto productRequest) throws Exception {
		return scmProductManagementService.getAllUserCreatedSkus(productRequest);
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public SkuDefinition updateSku( HttpServletRequest request,@RequestBody final SkuDefinition skuDefinition) throws Exception {
		return scmProductManagementService.updateSku(skuDefinition, getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku/update/V2", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public SkuDefinition updateSkuV2(HttpServletRequest request, @RequestBody final SkuDefinition skuDefinition) throws Exception {
		return scmProductManagementService.updateSkuV2(skuDefinition, getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku/cancel-user", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean CancelSkuByUser( HttpServletRequest request,@RequestBody final SkuDefinition skuDefinition) throws Exception {
		return scmProductManagementService.cancelSkuByUser(skuDefinition, getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.PUT, value = "sku-activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean activateSku(@RequestBody final int skuDefinitionId) {
		return scmProductManagementService.activateSku(skuDefinitionId);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "sku-deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean deactivateSku(@RequestBody final int skuDefinitionId) {
		return scmProductManagementService.deactivateSku(skuDefinitionId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload-sku-image", consumes = MediaType.MULTIPART_FORM_DATA)
	public String uploadSkuImage(HttpServletRequest request,
									  @RequestParam(value = "mimeType") MimeType mimeType,
									  @RequestParam(value = "skuId") Integer skuId,
									  @RequestParam(value = "file") final MultipartFile file) throws SumoException {
		return scmProductManagementService.uploadSkuImage(mimeType, skuId, file);
	}

	// Sku Packaging Mapping Resources

	@RequestMapping(method = RequestMethod.GET, value = "sku-packaging-mappings", produces = MediaType.APPLICATION_JSON)
	public Map<Integer, List<SkuPackagingMapping>> viewAllSkuPackagingMapping() {
		return scmProductManagementService.viewAllSkuPackagingMapping();
	}

	@RequestMapping(method = RequestMethod.GET, value = "sku-packaging-mapping", produces = MediaType.APPLICATION_JSON)
	public SkuPackagingMapping viewSkuPackagingMapping(@RequestParam final int skuPackagingMappingId) {
		return scmProductManagementService.viewSkuPackagingMapping(skuPackagingMappingId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku-packaging-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public List<SkuPackagingMapping> addNewSkuPackagingMapping(
			@RequestBody final List<SkuPackagingMapping> skuPackagingMappings) throws SumoException {
		return scmProductManagementService.addNewSkuPackagingMapping(skuPackagingMappings);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "sku-packaging-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean updateSkuPackagingMapping(@RequestBody final List<SkuPackagingMapping> skuPackagingMappings) {
		return scmProductManagementService.updateSkuPackagingMapping(skuPackagingMappings);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "sku-packaging-mapping-activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean activateSkuPackagingMapping(@RequestBody final int skuPackagingMappingId) {
		return scmProductManagementService.activateSkuPackagingMapping(skuPackagingMappingId);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "sku-packaging-mapping-deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean deactivateSkuPackagingMapping(@RequestBody final int skuPackagingMappingId) {
		return scmProductManagementService.deactivateSkuPackagingMapping(skuPackagingMappingId);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "set-default-sku-packaging-mapping", produces = MediaType.APPLICATION_JSON)
	public boolean setDefaultSkuPackaging(@RequestBody final int skuPackagingMappingId) throws DataUpdationException {
		return scmProductManagementService.setDefaultSkuPackaging(skuPackagingMappingId);
	}

	// Sku Attribute Value Resources

	@RequestMapping(method = RequestMethod.GET, value = "sku-attribute-values", produces = MediaType.APPLICATION_JSON)
	public Map<Integer, List<SkuAttributeValue>> viewAllSkuAttributeValues() {
		return scmProductManagementService.viewAllSkuAttributeValues();
	}

	@RequestMapping(method = RequestMethod.GET, value = "sku-attribute-value", produces = MediaType.APPLICATION_JSON)
	public SkuAttributeValue viewSkuAttributeValue(@RequestParam(value = "id") final int skuAttributeValueId) {
		return scmProductManagementService.viewSkuAttributeValue(skuAttributeValueId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku-attribute-value", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean addNewSkuAttributeValues(@RequestBody final List<SkuAttributeValue> skuAttributeValues) throws SumoException {
		return scmProductManagementService.addNewSkuAttributeValues(skuAttributeValues);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "sku-attribute-value", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean updateSkuAttributeValues(@RequestBody final List<SkuAttributeValue> skuAttributeValues) {
		return scmProductManagementService.updateSkuAttributeValues(skuAttributeValues);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "sku-attribute-value-activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean activateSkuAttributeValue(@RequestBody final int skuAttributeValueId) {
		return scmProductManagementService.activateSkuAttributeValue(skuAttributeValueId);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "sku-attribute-value-deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean deactivateSkuAttributeValue(@RequestBody final int skuAttributeValueId) {
		return scmProductManagementService.deactivateSkuAttributeValue(skuAttributeValueId);
	}

	// Vendor Resources

	@RequestMapping(method = RequestMethod.GET, value = "vendor-details", produces = MediaType.APPLICATION_JSON)
	public List<VendorDetail> getAllVendorDetails() {
		return scmProductManagementService.getAllVendorDetails();
	}

	@RequestMapping(method = RequestMethod.GET, value = "vendor-details/unit", produces = MediaType.APPLICATION_JSON)
	public List<VendorDetail> getUnitVendorDetails(@RequestParam(name = "unitId") int unitId) {
		return scmProductManagementService.getUnitVendorDetails(unitId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor-details/unit/products", produces = MediaType.APPLICATION_JSON)
	public Map<Integer, Set<VendorDetail>> getUnitProductVendors(@RequestBody UnitProductsVO request)
			throws SumoException {
		return scmProductManagementService.getUnitProductVendors(request);
	}

	// SKU Pricing Resources

	@RequestMapping(method = RequestMethod.GET, value = "sku-price-update-event/get", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public PriceUpdateEventVO fetchSkuPriceUpdateEvents() {
		PriceUpdateEventVO vo = new PriceUpdateEventVO();
		vo.getEntries().addAll(scmProductPriceManagementService.getSkuPriceUpdateEvents());
		for (PriceUpdateEvent e : vo.getEntries()) {
			if (PriceUpdateEventStatus.INITIATED.equals(e.getEventStatus())) {
				vo.setHasEvents(true);
			}
		}
		return vo;
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku-price-update-event/get/entries", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public PriceUpdateEvent fetchSkuPriceUpdateEventsEntries(@RequestBody int eventId) {
		return scmProductPriceManagementService.getSkuPriceUpdateEvent(eventId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku-price-update-event/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean updateSkuPrice(@RequestHeader(value = "userId") Integer userId,
			@RequestHeader(value = "userName") String userName, @RequestBody PriceUpdateEvent event) throws SumoException {
		if (userId == null || userName == null) {
			return false;
		}
		int updatedEventId = scmProductPriceManagementService.updateSkuPriceEvent(event, userId, userName);
		return calculateUpdatedPrices(updatedEventId);
	}

	private boolean calculateUpdatedPrices(int updatedEventId) {
		boolean finalUpdate = false;
		boolean priceUpdate = scmProductPriceManagementService.calculateProductCost(updatedEventId);
		if (priceUpdate) {
			finalUpdate = scmProductPriceManagementService.calculateRecipeCost(updatedEventId);
		}
		return finalUpdate;
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku-price-update-event/approve", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean approveSkuPrice(@RequestHeader(value = "userId") Integer userId,
			@RequestHeader(value = "userName") String userName, @RequestBody int eventId) throws IOException, SumoException {
		if (userId == null || userName == null) {
			return false;
		}
		return scmProductPriceManagementService.updateSkuPriceEvent(eventId, userId, userName,
				PriceUpdateEventStatus.APPROVED);
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku-price-update-event/reject", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean rejectSkuPrice(@RequestHeader(value = "userId") Integer userId,
			@RequestHeader(value = "userName") String userName, @RequestBody int eventId) throws IOException, SumoException {
		if (userId == null || userName == null) {
			return false;
		}
		return scmProductPriceManagementService.updateSkuPriceEvent(eventId, userId, userName,
				PriceUpdateEventStatus.REJECTED);
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku-price-update-event/cancel", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean cancelSkuPrice(@RequestHeader(value = "userId") Integer userId,
			@RequestHeader(value = "userName") String userName, @RequestBody int eventId) throws IOException, SumoException {
		if (userId == null || userName == null) {
			return false;
		}
		return scmProductPriceManagementService.updateSkuPriceEvent(eventId, userId, userName,
				PriceUpdateEventStatus.CANCELLED);
	}

	@RequestMapping(method = RequestMethod.POST, value = "sku-price-update-event/upload", consumes = MediaType.MULTIPART_FORM_DATA)
	public boolean uploadSkuPriceUpdateFile(@RequestHeader(value = "userId") Integer userId,
			@RequestHeader(value = "userName") String userName, @RequestParam(value = "file") final MultipartFile file)
			throws Exception {
		if (userId == null || userName == null) {
			return false;
		}
		Integer updatedEventId = null;
		try {
			PriceUpdateEvent event = MultiPartFileHelper.parseFile(env.getBasePath(), file);

			if (event == null || event.getEntries().isEmpty()) {
				return false;
			}
			// set user
			event.setCreatedBy(userId);
			event.setCreatedByName(userName);
			event.setCreationTime(AppUtils.getCurrentTimestamp());
			event.setEventActionType(PriceUpdateEventActionType.MANUAL);
			event.setEventStatus(PriceUpdateEventStatus.INITIATED);
			event.setEventType(PriceUpdateEventType.SKU_PRICE_UPDATE);
			event.setNoOfRecords(event.getEntries().size());
			event.setNoOfErrors(0);
			updatedEventId = scmProductPriceManagementService.addSkuPriceUpdateFile(event);
			if (updatedEventId == null) {
				return false;
			}
			return calculateUpdatedPrices(updatedEventId);
		} catch (Exception e) {
			if (updatedEventId != null) {
				// fail event
				scmProductPriceManagementService.failEvent(updatedEventId);
			}
			throw e;
		}

	}

	@RequestMapping(method = RequestMethod.GET, value = "sku-price-data/download")
	public View downloadSkuPriceUpdateFile() throws DataNotFoundException {
		return scmProductPriceManagementService.downloadSkuPriceUpdateFile();
	}

	@RequestMapping(method = RequestMethod.POST, value = "recipe/cost/key", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public RecipeCost calculateRecipeCost(@RequestBody ProductRecipeKey recipe, @RequestParam(required = false) String region) throws DataNotFoundException {
		return scmProductPriceManagementService.calculateRecipeCost(recipe, Objects.nonNull(region) ? region : "NCR");
	}

	@RequestMapping(method = RequestMethod.POST, value = "recipe/cost/detail", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public RecipeCost calculateRecipeCost(@RequestBody RecipeDetail detail) throws DataNotFoundException {
		return scmProductPriceManagementService.calculateRecipeCost(detail);
	}

	@RequestMapping(method = RequestMethod.POST, value = "recipe/cost/detail/new", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public RecipeCost calculateRecipeCost(@RequestBody RecipeDetail detail, @RequestParam(required = false) String region) throws DataNotFoundException {
		return scmProductPriceManagementService.calculateRecipeCostNew(detail, Objects.nonNull(region) ? region : "NCR");
	}

	@Scheduled(cron = "0 20 06 * * *", zone = "GMT+05:30")
	public void recalculateRecipePrices() {
		try {
			recalculateRecipePriceData();
		} catch (Exception e) {
			LOG.error("Error while recalculating recipe costs", e);
		}
	}

	@RequestMapping(method = RequestMethod.GET, value = "recipe/cost/recalculate", produces = MediaType.APPLICATION_JSON)
	public boolean recalculateRecipePricesAPI() throws IOException, SumoException {
		recalculateRecipePriceData();
		return true;
	}

	private void recalculateRecipePriceData() throws IOException, SumoException {
		long startTime = System.currentTimeMillis();
		Map<Integer, Map<Integer, ProductPriceData>> unitPriceMap = scmProductPriceManagementService.getUnitPriceMap();
		for (Integer unitId : unitPriceMap.keySet()) {
			long a = System.currentTimeMillis();
			scmProductPriceManagementService.recalculateRecipePrices(unitId, unitPriceMap.get(unitId));
			System.out.println("Unit-- " + unitId);
			System.out.println(String.format("Iteration ,%d, milliseconds ", System.currentTimeMillis() - a));
		}
		System.out.println(String.format("Total Time ,%d, milliseconds ", System.currentTimeMillis() - startTime));
	}

	@RequestMapping(method = RequestMethod.GET, value = "productDetail/productId", produces = MediaType.APPLICATION_JSON)
	public PlanOrderItem productForUnitId(@RequestParam(name = "productId") int productId,
										  @RequestParam(name = "unitId") int unitId) throws SumoException {
		return scmProductManagementService.getProductForUnit(productId, unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "unit-sku-packaging-mappings", produces = MediaType.APPLICATION_JSON)
	public Map<Integer,Integer> getUnitSkuPackagingMappings(@RequestParam int unitId) throws SumoException {
		return scmProductManagementService.getUnitSkuPackagingMappings(unitId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "sub-category-shelf-life", produces = MediaType.APPLICATION_JSON)
	public Boolean updateSubCategoryShelfLife(@RequestParam Integer id , @RequestParam Integer shelfLife , @RequestParam String range) throws SumoException {
		return scmProductManagementService.updateSubCategoryShelfLife(id,shelfLife,range);

	}

	@GetMapping("get-product-basic-detail")
	public ApiResponse getProductBasicDetail() {
		return scmProductManagementService.getProductBasicDetail();
	}

	@GetMapping("get-all-derived-products")
	public ApiResponse getAllDerivedProducts() {
		List<IdCodeName> data = scmProductManagementService.getAllDerivedProducts();
		ApiResponse response = new ApiResponse(data);
		if(CollectionUtils.isEmpty(data)) {
			response.setMessage("No derived products found");
			response.setSuccess(false);
		}
		return response;
	}

	@GetMapping("get-derived-products-mapping-for-unit")
	public ApiResponse getDerivedProductsMappingForUnit(@RequestParam Integer unitId) {
		List<UnitDerivedProductMappingDto> data = scmProductManagementService.getDerivedProductsMappingForUnit(unitId);
		ApiResponse response = new ApiResponse(data);
		if(CollectionUtils.isEmpty(data)) {
			response.setMessage("No derived products mapping found for unit: " + unitId);
			response.setSuccess(false);
		}
		return response;
	}

	@GetMapping("get-all-units-for-product-mapping")
	public ApiResponse getAllUnitsForProductMapping(@RequestParam Integer productId) {
		return new ApiResponse( scmProductManagementService.getAllUnitsForProductMapping(productId));
	}

	@PostMapping("update-derived-mapping-data")
	public ApiResponse updateDerivedMappingData(@RequestBody List<UnitDerivedProductMappingDto> request) {
		return scmProductManagementService.updateDerivedMappingData(request);
	}

	@GetMapping("get-derived-product-cloning-for-units")
	public ApiResponse getDerivedProductCloningForUnits(@RequestParam Integer fromUnitId) {
		List<UnitDerivedProductMappingDto> data = scmProductManagementService.getDerivedProductCloningForUnits(fromUnitId);
		ApiResponse response = new ApiResponse(data);
		if(CollectionUtils.isEmpty(data)) {
			response.setMessage("No derived product cloning found for unit: " + fromUnitId);
			response.setSuccess(false);
		}
		return response;
	}

	@GetMapping("get-units-cloning-for-product")
	public ApiResponse getUnitsCloningForProduct(@RequestParam Integer fromProductId) {
		List<UnitDerivedProductMappingDto> data = scmProductManagementService.getUnitsCloningForProduct(fromProductId);
		ApiResponse response = new ApiResponse(data);
		if(CollectionUtils.isEmpty(data)) {
			response.setMessage("No units cloning found for product: " + fromProductId);
			response.setSuccess(false);
		}
		return response;
	}

	@PostMapping("clone-derived-product-mappings")
	public ApiResponse cloneDerivedProductMappings(@RequestBody List<UnitDerivedProductMappingDto> request, @RequestParam Integer valueId, @RequestParam String valueType) {
		return scmProductManagementService.cloneDerivedProductMappings(request, valueId, valueType);
	}

    @PostMapping("get-region-product-packagings")
    public ApiResponse getRegionProductPackagings(@RequestBody RPPMappingRequest request) throws SumoException {
        return scmProductManagementService.getRegionProductPackagings(request);
    }

    @PostMapping("get-product-packaging-mapping")
    public ResponseEntity<ApiResponse> getProductPackagingMapping(@RequestBody RPPMappingRequest request) {
        ApiResponse response = new ApiResponse( scmProductManagementService.getProductPackagingMapping(request) );
        return ResponseEntity.ok(response);
    }

    @PostMapping("map-region-product-packaging")
    public ApiResponse mapRegionProductPackaging(@RequestBody List<RegionProductPackagingMapping> request) {
        return scmProductManagementService.mapRegionProductPackaging(request);
    }

}
